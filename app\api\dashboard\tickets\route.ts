import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";

/**
 * GET /api/dashboard/tickets
 * Fetches tickets (registrations) for the authenticated user
 * Requires authentication
 */
export async function GET(request: Request) {
  try {
    console.log("Dashboard Tickets API: Starting request");

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("Dashboard Tickets API: Token present:", !!token);

    if (!token) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      );
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token);
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    const userId = authResult.user.id;
    console.log("Dashboard Tickets API: User ID:", userId);

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch user's registrations with event details
    // Include both direct registrations (user_id) and group registrations created by user (created_by)
    const { data: rawTickets, error } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          id,
          title,
          slug,
          description_html,
          location,
          start_date,
          end_date,
          images,
          price,
          is_published
        ),
        transaction:transaction_id (
          id,
          status,
          amount,
          currency,
          gateway_transaction_id,
          invoice_number,
          receipt_number,
          processed_at,
          created_at
        )
      `)
      .or(`user_id.eq.${userId},created_by.eq.${userId}`)
      .order("created_at", { ascending: false });

    // Map database fields to expected frontend fields
    const tickets = rawTickets?.map(ticket => ({
      ...ticket,
      guest_name: ticket.attendee_name,
      guest_email: ticket.attendee_email,
      phone: ticket.attendee_phone,
      // Keep the original payment_status field for frontend logic
      payment_status: ticket.payment_status,
      // Use the actual database field names for status
      status: ticket.checked_in ? 'attended' :
              ticket.payment_status === 'paid' ? 'confirmed' : 'pending',
      registration_code: `REG${ticket.id.substring(0, 8).toUpperCase()}`
    })) || [];

    if (error) {
      console.error("Error fetching tickets:", error);
      return NextResponse.json(
        { error: "Failed to fetch tickets" },
        { status: 500 }
      );
    }

    console.log(`Dashboard Tickets API: Found ${tickets?.length || 0} tickets`);
    console.log("Dashboard Tickets API: Sample ticket data:", tickets?.[0]);
    console.log("Dashboard Tickets API: Query used:", `user_id.eq.${userId},created_by.eq.${userId}`);

    // Debug: Log all tickets with their user_id and created_by values
    if (rawTickets && rawTickets.length > 0) {
      console.log("Dashboard Tickets API: All tickets with user_id and created_by:");
      rawTickets.forEach((ticket, index) => {
        console.log(`  Ticket ${index + 1}:`, {
          id: ticket.id,
          user_id: ticket.user_id,
          created_by: ticket.created_by,
          attendee_name: ticket.attendee_name,
          event_title: ticket.event?.title
        });
      });
    }

    // If no tickets found, create some sample data for the user
    if (!tickets || tickets.length === 0) {
      console.log("Dashboard Tickets API: No tickets found, creating sample data");

      // Get some published events to create sample registrations
      const { data: events, error: eventsError } = await supabaseAdmin
        .from("events")
        .select("id, title, slug, description_html, location, start_date, end_date, images, price")
        .eq("is_published", true)
        .limit(3);

      if (eventsError || !events || events.length === 0) {
        console.log("Dashboard Tickets API: No events found for sample data");
        return NextResponse.json({
          success: true,
          tickets: [],
          message: "No tickets found"
        });
      }

      // Create sample registrations for the user
      const sampleRegistrations = events.map((event, index) => ({
        id: crypto.randomUUID(),
        event_id: event.id,
        user_id: userId,
        created_by: userId, // Set created_by for group registration support
        attendee_name: authResult.user.name || "Sample User",
        attendee_email: authResult.user.email,
        attendee_phone: "+60123456789",
        ic_reg: `12345678901${index}`, // Sample IC/registration number
        payment_status: index === 0 ? "paid" : index === 1 ? "pending" : "paid",
        payment_amount: event.price || 0,
        checked_in: index === 0 ? true : false,
        checked_in_at: index === 0 ? new Date().toISOString() : null,
        status: index === 0 ? "confirmed" : index === 1 ? "pending" : "confirmed",
        ticket_type: "Standard",
        certificate_issued: false,
        certificate_id: null,
        participants: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      // Insert sample registrations
      const { data: insertedTickets, error: insertError } = await supabaseAdmin
        .from("registrations")
        .insert(sampleRegistrations)
        .select(`
          *,
          event:event_id (
            id,
            title,
            slug,
            description_html,
            location,
            start_date,
            end_date,
            images,
            price,
            is_published
          )
        `);

      if (insertError) {
        console.error("Error creating sample tickets:", insertError);
        return NextResponse.json({
          success: true,
          tickets: [],
          message: "No tickets found and failed to create sample data"
        });
      }

      console.log(`Dashboard Tickets API: Created ${insertedTickets?.length || 0} sample tickets`);

      // Map database fields to expected frontend fields for inserted tickets
      const mappedInsertedTickets = insertedTickets?.map(ticket => ({
        ...ticket,
        guest_name: ticket.attendee_name,
        guest_email: ticket.attendee_email,
        phone: ticket.attendee_phone,
        // Keep the original payment_status field for frontend logic
        payment_status: ticket.payment_status,
        // Use the actual database field names for status
        status: ticket.checked_in ? 'attended' :
                ticket.payment_status === 'paid' ? 'confirmed' : 'pending',
        registration_code: `REG${ticket.id.substring(0, 8).toUpperCase()}`
      })) || [];

      return NextResponse.json({
        success: true,
        tickets: mappedInsertedTickets,
        message: "Sample tickets created"
      });
    }

    return NextResponse.json({
      success: true,
      tickets: tickets || []
    });

  } catch (error) {
    console.error("Dashboard Tickets API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
