import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import jwt from "jsonwebtoken";

export async function DELETE(request: Request) {
  try {
    console.log("Event image deletion started");

    // Get token from Authorization header
    const authHeader = request.headers.get("Authorization");
    console.log("Auth header present:", !!authHeader);

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("Missing or invalid auth header");
      return NextResponse.json(
        { error: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix
    console.log("Token extracted, length:", token.length);

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key");
      console.log("Token verified, userId:", decoded.userId);
    } catch (jwtError) {
      console.error("JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { imageUrl } = body;

    console.log("Image URL to delete:", imageUrl);

    if (!imageUrl) {
      console.log("No image URL provided");
      return NextResponse.json(
        { error: "Image URL is required" },
        { status: 400 }
      );
    }

    // Validate that this is an events storage URL
    if (!imageUrl.includes('/storage/v1/object/public/events/img/')) {
      console.log("Invalid image URL format:", imageUrl);
      return NextResponse.json(
        { error: "Invalid image URL format" },
        { status: 400 }
      );
    }

    // Extract the file path from the URL
    // URL format: https://[project].supabase.co/storage/v1/object/public/events/img/filename
    const urlParts = imageUrl.split('/storage/v1/object/public/events/');
    if (urlParts.length !== 2) {
      console.log("Could not parse image URL:", imageUrl);
      return NextResponse.json(
        { error: "Could not parse image URL" },
        { status: 400 }
      );
    }

    const filePath = urlParts[1]; // This should be "img/filename"
    console.log("Extracted file path:", filePath);

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();
    console.log("Supabase admin client created");

    // Delete the file from storage
    console.log("Deleting file from events bucket...");
    const { error: deleteError } = await supabaseAdmin.storage
      .from("events")
      .remove([filePath]);

    if (deleteError) {
      console.error("Delete error:", deleteError);
      return NextResponse.json(
        { error: `Failed to delete image: ${deleteError.message}` },
        { status: 500 }
      );
    }

    console.log("Image deleted successfully:", filePath);

    return NextResponse.json({
      success: true,
      message: "Image deleted successfully",
      deletedPath: filePath,
    });

  } catch (error: any) {
    console.error("Delete event image error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
